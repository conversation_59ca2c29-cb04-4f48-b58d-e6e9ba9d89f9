from bigmodule import M

# <aistudiograph>

# @param(id="m7", name="initialize")
# 交易引擎：初始化函数，只执行一次
def m7_initialize_bigquant_run(context):
    from bigtrader.finance.commission import PerOrder

    # 系统已经设置了默认的交易手续费和滑点，要修改手续费可使用如下函数
    context.set_commission(PerOrder(buy_cost=0.0003, sell_cost=0.0013, min_cost=5))
# @param(id="m7", name="before_trading_start")
# 交易引擎：每个单位时间开盘前调用一次。
def m7_before_trading_start_bigquant_run(context, data):
    # 盘前处理，订阅行情等
    pass

# @param(id="m7", name="handle_tick")
# 交易引擎：tick数据处理函数，每个tick执行一次
def m7_handle_tick_bigquant_run(context, tick):
    pass

# @param(id="m7", name="handle_data")
def m7_handle_data_bigquant_run(context, data):
    import pandas as pd

    # 下一个交易日不是调仓日，则不生成信号
    if not context.rebalance_period.is_signal_date(data.current_dt.date()):
        return

    # 从传入的数据 context.data 中读取今天的信号数据
    today_df = context.data[context.data["date"] == data.current_dt.strftime("%Y-%m-%d")]

    # 卖出不在目标持有列表中的股票
    for instrument in sorted(set(context.get_account_positions().keys()) - set(today_df["instrument"])):
        context.order_target_percent(instrument, 0)
    # 买入目标持有列表中的股票
    for i, x in today_df.iterrows():
        context.order_target_percent(x.instrument, 0.0 if pd.isnull(x.position) else x.position)
# @param(id="m7", name="handle_trade")
# 交易引擎：成交回报处理函数，每个成交发生时执行一次
def m7_handle_trade_bigquant_run(context, trade):
    pass

# @param(id="m7", name="handle_order")
# 交易引擎：委托回报处理函数，每个委托变化时执行一次
def m7_handle_order_bigquant_run(context, order):
    pass

# @param(id="m7", name="after_trading")
# 交易引擎：盘后处理函数，每日盘后执行一次
def m7_after_trading_bigquant_run(context, data):
    pass

# @module(position="-79,-174", comment="""因子特征，用表达式构建因子""")
m1 = M.input_features_dai.v30(
    mode="""SQL""",
    expr_tables="""cn_stock_prefactors""",
    extra_fields="""date, instrument""",
    order_by="""date, instrument""",
    expr_drop_na=True,
    sql="""SELECT
    date, instrument,position
FROM
    smart_core_num_3
ORDER BY date, instrument
""",
    extract_data=False,
    m_name="""m1"""
)

# @module(position="-127,46", comment="""抽取预测数据""")
m4 = M.extract_data_dai.v18(
    sql=m1.data,
    start_date="""2025-01-01""",
    start_date_bound_to_trading_date=True,
    end_date="""2025-06-15""",
    end_date_bound_to_trading_date=True,
    before_start_days=90,
    keep_before=False,
    debug=False,
    m_name="""m4"""
)

# @module(position="-108,245", comment="""交易，日线，设置初始化函数和K线处理函数，以及初始化资金、基准等""")
m7 = M.bigtrader.v35(
    data=m4.data,
    start_date="""""",
    end_date="""""",
    initialize=m7_initialize_bigquant_run,
    before_trading_start=m7_before_trading_start_bigquant_run,
    handle_tick=m7_handle_tick_bigquant_run,
    handle_data=m7_handle_data_bigquant_run,
    handle_trade=m7_handle_trade_bigquant_run,
    handle_order=m7_handle_order_bigquant_run,
    after_trading=m7_after_trading_bigquant_run,
    capital_base=1000000,
    frequency="""daily""",
    product_type="""股票""",
    rebalance_period_type="""交易日""",
    rebalance_period_days="""5""",
    rebalance_period_roll_forward=True,
    backtest_engine_mode="""标准模式""",
    before_start_days=0,
    volume_limit=1,
    order_price_field_buy="""open""",
    order_price_field_sell="""open""",
    benchmark="""沪深300指数""",
    plot_charts=True,
    debug=False,
    backtest_only=False,
    m_name="""m7"""
)
# </aistudiograph>