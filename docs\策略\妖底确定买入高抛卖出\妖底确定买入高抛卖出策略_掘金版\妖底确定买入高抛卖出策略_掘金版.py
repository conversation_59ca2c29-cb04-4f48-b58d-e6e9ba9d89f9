# coding=utf-8
"""
妖底确定买入高抛卖出量化策略 - 掘金平台版本
结合妖底确定选股和高抛低吸卖出指标的量化交易策略

策略逻辑：
1. 每日收盘前5分钟(14:55)使用妖底确定选股筛选买入标的
2. 买入账户总资产的10%，已持有股票可继续加仓
3. 收盘前5分钟检测持仓股票是否触发卖出信号
4. 如有卖出信号则全仓卖出

适配掘金平台特点：
- 使用掘金API获取数据和执行交易
- 保持原有策略逻辑不变
- 支持回测和实盘交易
"""

from __future__ import print_function, absolute_import, unicode_literals
from gm.api import *
import pandas as pd
import numpy as np
import datetime
import random

# 尝试导入talib，如果不可用则使用备用方案
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("talib不可用，将使用备用MACD计算方法")


def init(context):
    """
    策略初始化函数
    """
    # 策略配置
    context.position_ratio = 0.08  # 单次买入仓位比例（账户总资产的8%，降低集中度风险）
    context.trade_time = "14:55"  # 交易时间
    context.commission_rate = 0.0003  # 手续费率
    context.stamp_tax = 0.001  # 印花税
    context.min_commission = 5  # 最小手续费

    # === 止损参数配置 ===
    context.stop_loss_ratio = -0.15  # 固定止损比例：-15%
    context.trailing_stop_ratio = 0.08  # 移动止损比例：8%
    context.profit_protect_threshold = 0.10  # 盈利保护阈值：10%
    context.max_holding_days = 60  # 最大持仓天数
    context.portfolio_stop_loss = -0.20  # 组合止损比例：-20%
    context.max_single_position = 0.08  # 单股最大持仓比例：8%

    # === 风险控制参数 ===
    context.max_positions = 15  # 最大持仓股票数量
    context.daily_loss_limit = -0.05  # 单日亏损限制：-5%

    # === 记录变量 ===
    context.prev_nav = None  # 记录前一日净值
    context.daily_start_nav = None  # 记录当日开始净值
    context.position_entry_dates = {}  # 记录每个持仓的买入日期
    context.position_highest_prices = {}  # 记录每个持仓的最高价格（用于移动止损）
    context.stop_loss_triggered_today = False  # 当日是否触发过止损

    # === 市场环境相关 ===
    context.market_state = "NORMAL"  # 市场状态：BULL/BEAR/SHOCK/NORMAL
    context.market_trend_days = 0  # 当前趋势持续天数
    context.benchmark_prices = []  # 基准价格历史（用于判断市场环境）
    context.volatility_window = 20  # 波动率计算窗口

    # === 动态参数（会根据市场环境调整）===
    context.dynamic_stop_loss = context.stop_loss_ratio  # 动态止损比例
    context.dynamic_position_ratio = context.position_ratio  # 动态仓位比例
    context.dynamic_max_positions = context.max_positions  # 动态最大持仓数

    # === 资金管理相关 ===
    context.trade_history = []  # 交易历史记录
    context.win_rate = 0.5  # 胜率
    context.avg_return = 0.0  # 平均收益率
    context.recent_performance = []  # 近期表现记录

    # === 性能优化相关 ===
    context.data_cache = {}  # 数据缓存
    context.cache_expire_time = {}  # 缓存过期时间
    context.error_count = {}  # 错误计数
    context.max_errors_per_stock = 3  # 单股最大错误次数

    # 每日定时任务 - 收盘前3分钟执行策略
    schedule(schedule_func=trade_strategy, date_rule='1d', time_rule='14:57:00')

    # 盘中止损检查 - 每30分钟检查一次
    schedule(schedule_func=intraday_stop_loss_check, date_rule='1d', time_rule='10:00:00')
    schedule(schedule_func=intraday_stop_loss_check, date_rule='1d', time_rule='10:30:00')
    schedule(schedule_func=intraday_stop_loss_check, date_rule='1d', time_rule='11:00:00')
    schedule(schedule_func=intraday_stop_loss_check, date_rule='1d', time_rule='11:30:00')
    schedule(schedule_func=intraday_stop_loss_check, date_rule='1d', time_rule='13:30:00')
    schedule(schedule_func=intraday_stop_loss_check, date_rule='1d', time_rule='14:00:00')
    schedule(schedule_func=intraday_stop_loss_check, date_rule='1d', time_rule='14:30:00')

    print('妖底确定买入高抛卖出策略（优化版）初始化完成')
    print(f'策略参数: 单次买入仓位比例={context.position_ratio}')
    print(f'止损参数: 固定止损={context.stop_loss_ratio}, 移动止损={context.trailing_stop_ratio}')
    print(f'风险控制: 最大持仓数={context.max_positions}, 单股最大比例={context.max_single_position}')


def intraday_stop_loss_check(context):
    """
    盘中止损检查函数
    """
    try:
        # 检查是否已经触发过当日止损
        if context.stop_loss_triggered_today:
            return

        # 获取当前账户信息
        account = context.account()
        current_nav = account.cash['nav']

        # 初始化当日开始净值
        if context.daily_start_nav is None:
            context.daily_start_nav = current_nav

        # 检查单日亏损限制
        daily_pnl_ratio = (current_nav - context.daily_start_nav) / context.daily_start_nav
        if daily_pnl_ratio <= context.daily_loss_limit:
            print(f"触发单日亏损限制 {daily_pnl_ratio:.2%}，暂停交易")
            context.stop_loss_triggered_today = True
            return

        # 检查组合回撤止损
        if context.prev_nav is not None:
            portfolio_drawdown = (current_nav - context.prev_nav) / context.prev_nav
            if portfolio_drawdown <= context.portfolio_stop_loss:
                print(f"触发组合回撤止损 {portfolio_drawdown:.2%}，执行减仓")
                execute_portfolio_stop_loss(context)
                return

        # 检查个股止损
        check_individual_stop_loss(context)

    except Exception as e:
        print(f"盘中止损检查出错: {e}")


def trade_strategy(context):
    """
    主交易策略函数
    """
    try:
        print(f"\n=== {context.now} 策略执行开始 ===")

        # 重置当日止损标志
        context.stop_loss_triggered_today = False
        context.daily_start_nav = None

        # 0. 更新市场环境判断
        update_market_environment(context)

        # 1. 优先检查止损信号
        check_all_stop_loss(context)

        # 2. 检查技术卖出信号
        check_sell_signals(context)

        # 3. 检查买入信号（如果没有触发止损）
        if not context.stop_loss_triggered_today:
            check_buy_signals(context)

        # 4. 更新持仓追踪信息
        update_position_tracking(context)

        # 5. 记录当前持仓状态
        log_positions(context)

        print("=== 策略执行完成 ===\n")

    except Exception as e:
        print(f"策略执行出错: {e}")



def sma_tdx(series, period, weight=1):
    """
    通达信SMA函数的精确实现
    SMA(X, N, M) = (M*X + (N-M)*SMA')/N
    其中SMA'是前一个SMA值
    """
    return series.ewm(alpha=weight/period, adjust=False).mean()


def filter_tdx(condition, period):
    """
    通达信FILTER函数的精确实现
    FILTER(X, N): 当X条件成立时，将其后N周期内的数据置为0
    这里用rolling sum > 0来近似，但可能存在差异
    """
    return condition.rolling(period).sum() > 0


def cross_tdx(series1, series2):
    """
    通达信CROSS函数的精确实现
    CROSS(A, B): A向上穿越B时返回1
    支持Series与Series、Series与数值的交叉
    """
    if isinstance(series2, (int, float)):
        # 数值与Series的交叉
        return (series1 > series2) & (series1.shift(1) <= series2)
    else:
        # Series与Series的交叉
        return (series1 > series2) & (series1.shift(1) <= series2.shift(1))


def check_all_stop_loss(context):
    """
    检查所有止损条件
    """
    try:
        # 检查个股止损
        check_individual_stop_loss(context)

        # 检查时间止损
        check_time_stop_loss(context)

        # 检查组合风险
        check_portfolio_risk(context)

    except Exception as e:
        print(f"检查止损条件出错: {e}")


def check_individual_stop_loss(context):
    """
    检查个股止损条件
    """
    try:
        positions = context.account().positions()
        if not positions:
            return

        # 获取当前价格数据
        symbols = [p.symbol for p in positions]
        current_data = current(symbols=symbols)
        current_price_map = {bar['symbol']: bar['price'] for bar in current_data if bar['price'] > 0}

        for position in positions:
            symbol = position.symbol
            current_price = current_price_map.get(symbol, 0)

            if current_price <= 0:
                continue

            # 计算盈亏比例
            cost_price = position.vwap
            pnl_ratio = (current_price - cost_price) / cost_price

            # 更新最高价格（用于移动止损）
            if symbol not in context.position_highest_prices:
                context.position_highest_prices[symbol] = current_price
            else:
                context.position_highest_prices[symbol] = max(context.position_highest_prices[symbol], current_price)

            # 检查固定止损（使用动态参数）
            if pnl_ratio <= context.dynamic_stop_loss:
                execute_stop_loss(context, symbol, f"固定止损({context.dynamic_stop_loss:.1%})", pnl_ratio)
                continue

            # 检查移动止损（仅在盈利时）
            if pnl_ratio > context.profit_protect_threshold:
                highest_price = context.position_highest_prices[symbol]
                trailing_stop_price = highest_price * (1 - context.trailing_stop_ratio)

                if current_price <= trailing_stop_price:
                    trailing_pnl = (current_price - cost_price) / cost_price
                    execute_stop_loss(context, symbol, "移动止损", trailing_pnl)
                    continue

    except Exception as e:
        print(f"检查个股止损出错: {e}")


def check_time_stop_loss(context):
    """
    检查时间止损条件
    """
    try:
        positions = context.account().positions()
        if not positions:
            return

        current_date = context.now.date()

        for position in positions:
            symbol = position.symbol

            # 获取持仓天数
            entry_date = context.position_entry_dates.get(symbol)
            if entry_date is None:
                # 如果没有记录，设为当前日期
                context.position_entry_dates[symbol] = current_date
                continue

            holding_days = (current_date - entry_date).days

            # 检查是否超过最大持仓天数
            if holding_days >= context.max_holding_days:
                current_data = current(symbols=[symbol])
                if current_data and current_data[0]['price'] > 0:
                    current_price = current_data[0]['price']
                    cost_price = position.vwap
                    pnl_ratio = (current_price - cost_price) / cost_price

                    execute_stop_loss(context, symbol, f"时间止损({holding_days}天)", pnl_ratio)

    except Exception as e:
        print(f"检查时间止损出错: {e}")


def check_portfolio_risk(context):
    """
    检查组合风险
    """
    try:
        account = context.account()
        positions = account.positions()

        if not positions:
            return

        total_nav = account.cash['nav']

        # 检查持仓集中度
        for position in positions:
            position_value = position.volume * position.vwap
            position_ratio = position_value / total_nav

            if position_ratio > context.max_single_position:
                print(f"持仓集中度过高: {position.symbol} 占比 {position_ratio:.2%}")
                # 可以选择减仓或者警告

        # 检查持仓数量
        if len(positions) > context.max_positions:
            print(f"持仓数量过多: {len(positions)} > {context.max_positions}")

    except Exception as e:
        print(f"检查组合风险出错: {e}")


def execute_stop_loss(context, symbol, reason, pnl_ratio):
    """
    执行止损操作
    """
    try:
        # 获取持仓信息用于记录交易结果
        positions = context.account().positions()
        position = None
        for p in positions:
            if p.symbol == symbol:
                position = p
                break

        # 获取股票名称
        try:
            inst = get_instrumentinfos(symbols=[symbol])
            stock_name = inst[0]['sec_name'] if inst else '未知名称'
        except:
            stock_name = '未知名称'

        # 记录交易结果
        if position:
            entry_date = context.position_entry_dates.get(symbol)
            holding_days = 0
            if entry_date:
                holding_days = (context.now.date() - entry_date).days

            current_data = current(symbols=[symbol])
            if current_data and current_data[0]['price'] > 0:
                current_price = current_data[0]['price']
                record_trade_result(context, symbol, position.vwap, current_price, holding_days)

        # 执行卖出
        order_target_percent(symbol=symbol,
                           percent=0,
                           position_side=PositionSide_Long,
                           order_type=OrderType_Market)

        print(f"执行止损: {symbol} ({stock_name}) - {reason}, 盈亏比例: {pnl_ratio:.2%}")

        # 清理追踪信息
        if symbol in context.position_entry_dates:
            del context.position_entry_dates[symbol]
        if symbol in context.position_highest_prices:
            del context.position_highest_prices[symbol]

    except Exception as e:
        print(f"执行止损出错 {symbol}: {e}")


def execute_portfolio_stop_loss(context):
    """
    执行组合止损（减仓）
    """
    try:
        positions = context.account().positions()
        if not positions:
            return

        # 按亏损程度排序，优先卖出亏损最大的
        position_pnl = []
        current_data = current(symbols=[p.symbol for p in positions])
        current_price_map = {bar['symbol']: bar['price'] for bar in current_data if bar['price'] > 0}

        for position in positions:
            current_price = current_price_map.get(position.symbol, 0)
            if current_price > 0:
                pnl_ratio = (current_price - position.vwap) / position.vwap
                position_pnl.append((position.symbol, pnl_ratio))

        # 按亏损程度排序
        position_pnl.sort(key=lambda x: x[1])

        # 卖出亏损最大的50%持仓
        sell_count = max(1, len(position_pnl) // 2)
        for i in range(sell_count):
            symbol, pnl_ratio = position_pnl[i]
            execute_stop_loss(context, symbol, "组合止损", pnl_ratio)

    except Exception as e:
        print(f"执行组合止损出错: {e}")


def update_position_tracking(context):
    """
    更新持仓追踪信息
    """
    try:
        positions = context.account().positions()
        current_symbols = {p.symbol for p in positions}
        current_date = context.now.date()

        # 为新持仓添加买入日期
        for symbol in current_symbols:
            if symbol not in context.position_entry_dates:
                context.position_entry_dates[symbol] = current_date

        # 清理已卖出股票的追踪信息
        symbols_to_remove = []
        for symbol in context.position_entry_dates:
            if symbol not in current_symbols:
                symbols_to_remove.append(symbol)

        for symbol in symbols_to_remove:
            del context.position_entry_dates[symbol]
            if symbol in context.position_highest_prices:
                del context.position_highest_prices[symbol]

    except Exception as e:
        print(f"更新持仓追踪信息出错: {e}")


def update_market_environment(context):
    """
    更新市场环境判断并动态调整策略参数
    """
    try:
        # 获取基准指数数据（沪深300）
        end_date = context.now.strftime('%Y-%m-%d')
        start_date = (context.now - datetime.timedelta(days=60)).strftime('%Y-%m-%d')

        # 获取沪深300指数数据
        benchmark_data = history(symbol='SHSE.000300', frequency='1d',
                                start_time=start_date, end_time=end_date,
                                fields='close', skip_suspended=True, df=True)

        if benchmark_data is None or len(benchmark_data) < 20:
            print("无法获取基准数据，使用默认参数")
            return

        closes = benchmark_data['close']
        current_price = closes.iloc[-1]

        # 计算技术指标
        ma5 = closes.rolling(5).mean().iloc[-1]
        ma20 = closes.rolling(20).mean().iloc[-1]
        ma60 = closes.rolling(60).mean().iloc[-1] if len(closes) >= 60 else ma20

        # 计算波动率
        returns = closes.pct_change().dropna()
        volatility = returns.rolling(context.volatility_window).std().iloc[-1] * (252 ** 0.5)

        # 计算趋势强度
        trend_strength = (current_price - ma20) / ma20

        # 判断市场状态
        prev_state = context.market_state

        if current_price > ma5 > ma20 > ma60 and trend_strength > 0.05:
            context.market_state = "BULL"  # 牛市
        elif current_price < ma5 < ma20 < ma60 and trend_strength < -0.05:
            context.market_state = "BEAR"  # 熊市
        elif abs(trend_strength) < 0.02 and volatility < 0.25:
            context.market_state = "SHOCK"  # 震荡市
        else:
            context.market_state = "NORMAL"  # 正常市场

        # 更新趋势持续天数
        if context.market_state == prev_state:
            context.market_trend_days += 1
        else:
            context.market_trend_days = 1

        # 根据市场环境动态调整参数
        adjust_strategy_parameters(context, volatility, trend_strength)

        print(f"市场环境: {context.market_state}, 趋势强度: {trend_strength:.2%}, 波动率: {volatility:.2%}")

    except Exception as e:
        print(f"更新市场环境出错: {e}")


def adjust_strategy_parameters(context, volatility, trend_strength):
    """
    根据市场环境动态调整策略参数
    """
    try:
        base_stop_loss = context.stop_loss_ratio
        base_position_ratio = context.position_ratio
        base_max_positions = context.max_positions

        if context.market_state == "BULL":
            # 牛市：适当放宽止损，增加仓位
            context.dynamic_stop_loss = base_stop_loss * 0.8  # 止损放宽到-12%
            context.dynamic_position_ratio = min(base_position_ratio * 1.2, 0.10)  # 仓位增加到9.6%
            context.dynamic_max_positions = min(base_max_positions + 3, 20)  # 最大持仓18只

        elif context.market_state == "BEAR":
            # 熊市：收紧止损，减少仓位
            context.dynamic_stop_loss = base_stop_loss * 1.3  # 止损收紧到-11.5%
            context.dynamic_position_ratio = base_position_ratio * 0.6  # 仓位减少到4.8%
            context.dynamic_max_positions = max(base_max_positions - 5, 8)  # 最大持仓10只

        elif context.market_state == "SHOCK":
            # 震荡市：正常止损，减少仓位
            context.dynamic_stop_loss = base_stop_loss * 1.1  # 止损略微收紧到-13.6%
            context.dynamic_position_ratio = base_position_ratio * 0.8  # 仓位减少到6.4%
            context.dynamic_max_positions = base_max_positions - 2  # 最大持仓13只

        else:
            # 正常市场：使用基础参数
            context.dynamic_stop_loss = base_stop_loss
            context.dynamic_position_ratio = base_position_ratio
            context.dynamic_max_positions = base_max_positions

        # 根据波动率进一步调整
        if volatility > 0.35:  # 高波动率
            context.dynamic_stop_loss *= 1.2  # 进一步收紧止损
            context.dynamic_position_ratio *= 0.8  # 减少仓位
        elif volatility < 0.15:  # 低波动率
            context.dynamic_stop_loss *= 0.9  # 略微放宽止损
            context.dynamic_position_ratio *= 1.1  # 略微增加仓位

        # 根据策略表现进一步调整
        adjust_position_by_performance(context)

        # 确保参数在合理范围内
        context.dynamic_stop_loss = max(context.dynamic_stop_loss, -0.25)  # 最大止损-25%
        context.dynamic_stop_loss = min(context.dynamic_stop_loss, -0.08)  # 最小止损-8%
        context.dynamic_position_ratio = max(context.dynamic_position_ratio, 0.03)  # 最小仓位3%
        context.dynamic_position_ratio = min(context.dynamic_position_ratio, 0.12)  # 最大仓位12%
        context.dynamic_max_positions = max(context.dynamic_max_positions, 5)  # 最少5只
        context.dynamic_max_positions = min(context.dynamic_max_positions, 25)  # 最多25只

        print(f"动态参数调整: 止损={context.dynamic_stop_loss:.1%}, 仓位={context.dynamic_position_ratio:.1%}, 最大持仓={context.dynamic_max_positions}只")

    except Exception as e:
        print(f"调整策略参数出错: {e}")


def adjust_position_by_performance(context):
    """
    根据策略表现调整仓位大小
    """
    try:
        # 计算近期胜率和平均收益
        if len(context.trade_history) >= 10:
            recent_trades = context.trade_history[-20:]  # 最近20笔交易
            wins = sum(1 for trade in recent_trades if trade['return'] > 0)
            context.win_rate = wins / len(recent_trades)
            context.avg_return = sum(trade['return'] for trade in recent_trades) / len(recent_trades)

            # 根据胜率调整仓位
            if context.win_rate > 0.6:  # 胜率高
                context.dynamic_position_ratio *= 1.1  # 增加仓位
            elif context.win_rate < 0.4:  # 胜率低
                context.dynamic_position_ratio *= 0.8  # 减少仓位

            # 根据平均收益调整
            if context.avg_return > 0.05:  # 平均收益高
                context.dynamic_position_ratio *= 1.05
            elif context.avg_return < -0.02:  # 平均亏损
                context.dynamic_position_ratio *= 0.9

        # 根据账户回撤调整
        account = context.account()
        current_nav = account.cash['nav']
        if context.prev_nav is not None:
            drawdown = (current_nav - context.prev_nav) / context.prev_nav
            if drawdown < -0.1:  # 回撤超过10%
                context.dynamic_position_ratio *= 0.7  # 大幅减仓
            elif drawdown < -0.05:  # 回撤超过5%
                context.dynamic_position_ratio *= 0.85  # 适度减仓

    except Exception as e:
        print(f"根据表现调整仓位出错: {e}")


def record_trade_result(context, symbol, entry_price, exit_price, holding_days):
    """
    记录交易结果
    """
    try:
        trade_return = (exit_price - entry_price) / entry_price
        trade_record = {
            'symbol': symbol,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'return': trade_return,
            'holding_days': holding_days,
            'date': context.now.date()
        }

        context.trade_history.append(trade_record)

        # 只保留最近100笔交易记录
        if len(context.trade_history) > 100:
            context.trade_history = context.trade_history[-100:]

    except Exception as e:
        print(f"记录交易结果出错: {e}")


def get_cached_data(context, symbol, data_type, cache_minutes=30):
    """
    获取缓存数据
    """
    try:
        cache_key = f"{symbol}_{data_type}"
        current_time = context.now

        # 检查缓存是否存在且未过期
        if cache_key in context.data_cache:
            expire_time = context.cache_expire_time.get(cache_key)
            if expire_time and current_time < expire_time:
                return context.data_cache[cache_key]

        return None

    except Exception as e:
        print(f"获取缓存数据出错: {e}")
        return None


def set_cached_data(context, symbol, data_type, data, cache_minutes=30):
    """
    设置缓存数据
    """
    try:
        cache_key = f"{symbol}_{data_type}"
        context.data_cache[cache_key] = data
        context.cache_expire_time[cache_key] = context.now + datetime.timedelta(minutes=cache_minutes)

        # 清理过期缓存
        current_time = context.now
        expired_keys = []
        for key, expire_time in context.cache_expire_time.items():
            if current_time >= expire_time:
                expired_keys.append(key)

        for key in expired_keys:
            if key in context.data_cache:
                del context.data_cache[key]
            del context.cache_expire_time[key]

    except Exception as e:
        print(f"设置缓存数据出错: {e}")


def handle_stock_error(context, symbol, error_msg):
    """
    处理股票相关错误
    """
    try:
        if symbol not in context.error_count:
            context.error_count[symbol] = 0

        context.error_count[symbol] += 1

        if context.error_count[symbol] >= context.max_errors_per_stock:
            print(f"股票 {symbol} 错误次数过多({context.error_count[symbol]}次)，暂时跳过")
            return True  # 表示应该跳过这只股票

        return False

    except Exception as e:
        print(f"处理股票错误出错: {e}")
        return False


def safe_get_history_data(context, symbol, days=60, cache_minutes=30):
    """
    安全获取历史数据（带缓存和错误处理）
    """
    try:
        # 先尝试从缓存获取
        cached_data = get_cached_data(context, symbol, "history", cache_minutes)
        if cached_data is not None:
            return cached_data

        # 从API获取数据
        end_date = (context.now - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        start_date = (context.now - datetime.timedelta(days=days)).strftime('%Y-%m-%d')

        df = history(symbol=symbol, frequency='1d', start_time=start_date, end_time=end_date,
                    fields='symbol,eob,open,close,high,low,volume', skip_suspended=True,
                    fill_missing='Last', adjust=ADJUST_PREV, df=True)

        if df is not None and len(df) > 0:
            # 缓存数据
            set_cached_data(context, symbol, "history", df, cache_minutes)
            return df
        else:
            return None

    except Exception as e:
        if handle_stock_error(context, symbol, str(e)):
            return None
        print(f"获取 {symbol} 历史数据出错: {e}")
        return None


def check_buy_timing_filters(context, stock):
    """
    检查买入时机过滤条件（优化版本）
    """
    try:
        # 检查错误计数
        if stock in context.error_count and context.error_count[stock] >= context.max_errors_per_stock:
            return False

        # 使用缓存获取历史数据
        df = safe_get_history_data(context, stock, days=60, cache_minutes=30)

        if df is None or len(df) < 30:
            return False

        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']

        # 1. 避免追高：当前价格不能是近20日最高价
        current_price = close.iloc[-1]
        recent_high = high.tail(20).max()
        if current_price >= recent_high * 0.98:  # 接近最高价
            return False

        # 2. 成交量过滤：近5日平均成交量要大于前20日平均成交量
        recent_volume = volume.tail(5).mean()
        avg_volume = volume.tail(25).head(20).mean()
        if recent_volume < avg_volume * 1.2:  # 成交量放大不足
            return False

        # 3. 技术位置过滤：价格应在合理区间
        ma5 = close.rolling(5).mean().iloc[-1]
        ma20 = close.rolling(20).mean().iloc[-1]

        # 价格应该在5日线附近或之上，但不能过度偏离20日线
        if current_price < ma5 * 0.98:  # 跌破5日线太多
            return False
        if current_price > ma20 * 1.15:  # 偏离20日线过多
            return False

        # 4. 波动率过滤：避免异常波动的股票
        returns = close.pct_change().dropna()
        if len(returns) >= 10:
            recent_volatility = returns.tail(10).std()
            if recent_volatility > 0.08:  # 日波动率超过8%
                return False

        # 5. 根据市场环境调整过滤强度
        if context.market_state == "BEAR":
            # 熊市更严格的过滤
            if current_price > ma20:  # 熊市中价格必须在20日线下方
                return False
        elif context.market_state == "BULL":
            # 牛市相对宽松
            pass  # 使用基础过滤条件

        return True

    except Exception as e:
        print(f"检查买入时机过滤条件出错 {stock}: {e}")
        return False


def calculate_macd(close_prices, fast=12, slow=26, signal=9):
    """
    计算MACD指标，使用ewm以确保一致性
    """
    dif = close_prices.ewm(span=fast, adjust=False).mean() - close_prices.ewm(span=slow, adjust=False).mean()
    dea = dif.ewm(span=signal, adjust=False).mean()
    macd = (dif - dea) * 2
    return macd


def calculate_yaodi_signal(context, stock):
    """
    计算妖底确定买入信号 - 与通达信版逻辑完全对齐
    """
    try:
        # 获取足够的历史数据 - 避免未来函数，使用前一交易日作为结束日期
        end_date = (context.now - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        start_date = (context.now - datetime.timedelta(days=200)).strftime('%Y-%m-%d')
        
        df = history(symbol=stock, frequency='1d', start_time=start_date, end_time=end_date, 
                    fields='symbol,eob,open,close,high,low,volume', skip_suspended=True, 
                    fill_missing='Last', adjust=ADJUST_PREV, df=True)
        
        if df is None or len(df) < 100:
            return False
            
        # 重命名列以匹配公式
        df = df.rename(columns={'close': 'C', 'high': 'H', 'low': 'L'})
        
        C = df['C']
        H = df['H']
        L = df['L']
        
        # --- 核心指标计算 (与聚宽版对齐) ---

        # C1指标
        MA30 = C.rolling(30).mean()
        MA60 = C.rolling(60).mean()
        C1 = ((MA30 - L) / MA60) * 200

        # M2指标 - 使用通达信SMA精确实现
        price_changes = C.diff()
        abs_changes = price_changes.abs()
        positive_changes = price_changes.where(price_changes > 0, 0)
        M2 = sma_tdx(positive_changes, 7, 1) / sma_tdx(abs_changes, 7, 1) * 100

        # G1条件 - 使用通达信FILTER精确实现
        g1_base = (M2.shift(1) < 20) & (M2 > M2.shift(1))
        G1 = filter_tdx(g1_base, 5)

        # TU条件：超跌状态
        MA40 = C.rolling(40).mean()
        TU = C / MA40 < 0.74

        # TDJ条件：振幅大于5%
        TDJ = (H - L) / C.shift(1) > 0.05

        # YUL条件: 5日内TDJ次数>1
        YUL = TDJ.rolling(5).sum() > 1

        # QD启动条件
        QD = TU & TDJ & YUL

        # NTDF相关计算
        EMA5 = C.ewm(span=5, adjust=False).mean()
        SMMA = EMA5.ewm(span=5, adjust=False).mean()
        IM = EMA5.diff()
        TSMMA = SMMA.diff()
        DIVMA = (EMA5 - SMMA).abs()
        ET = (IM + TSMMA) / 2
        TDF = DIVMA * (ET ** 3)
        NTDF = TDF / TDF.abs().rolling(15).max()

        # QR确定条件 - 使用通达信CROSS精确实现
        QR = cross_tdx(NTDF, -0.9)

        # MACD 计算
        MACD = calculate_macd(C)

        # XG选股条件 - 使用通达信FILTER精确实现
        xg_base = QD.shift(1) & (QR | (C > C.shift(1))) & (MACD > -1.5)
        XG = filter_tdx(xg_base, 10)

        # BD波段条件 - 使用通达信FILTER精确实现
        bd_base = ((G1 & (C1 > 20)) | (C > C.shift(1))) & QD.shift(1)
        BD = filter_tdx(bd_base, 10)

        # 最终妖底确定条件: COUNT(XG,13)>=1 AND BD
        final_cond1 = XG.rolling(13).sum() >= 1
        final_cond2 = BD

        # --- 信号判断 ---
        if len(final_cond1) == 0 or len(final_cond2) == 0:
            return False

        last_signal1 = final_cond1.iloc[-1] if not pd.isna(final_cond1.iloc[-1]) else False
        last_signal2 = final_cond2.iloc[-1] if not pd.isna(final_cond2.iloc[-1]) else False

        return bool(last_signal1 and last_signal2)
        
    except Exception as e:
        print(f"计算妖底确定信号出错 {stock}: {e}")
        return False


def calculate_gaopao_sell_signal(context, stock):
    """
    计算高抛低吸卖出信号 - 与通达信卖出选股指标完全对齐
    """
    try:
        # 获取历史数据 - 避免未来函数，使用前一交易日作为结束日期
        end_date = (context.now - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        start_date = (context.now - datetime.timedelta(days=150)).strftime('%Y-%m-%d')

        df = history(symbol=stock, frequency='1d', start_time=start_date, end_time=end_date,
                    fields='symbol,eob,open,close,high,low,volume', skip_suspended=True,
                    fill_missing='Last', adjust=ADJUST_PREV, df=True)

        if df is None or len(df) < 80:
            return False

        # 重命名列
        df = df.rename(columns={'close': 'C', 'high': 'H', 'low': 'L'})

        C = df['C']
        H = df['H']
        L = df['L']

        # 参数设置
        N1 = 21
        N2 = 8

        # === 完整的通达信卖出选股指标计算 ===

        # VAR1: 3*SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1)-2*SMA(SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1),15,1)
        llv75 = L.rolling(75).min()
        hhv75 = H.rolling(75).max()
        denominator75 = hhv75 - llv75
        denominator75 = denominator75.where(denominator75 != 0, 1)

        base_ratio = (C - llv75) / denominator75 * 100
        sma20_1 = sma_tdx(base_ratio, 20, 1)
        sma15_1 = sma_tdx(sma20_1, 15, 1)
        VAR1 = 3 * sma20_1 - 2 * sma15_1

        # VAR2: (CLOSE-LLV(LOW,26))/(HHV(HIGH,26)-LLV(LOW,26))*100
        llv26 = L.rolling(26).min()
        hhv26 = H.rolling(26).max()
        denominator26 = hhv26 - llv26
        denominator26 = denominator26.where(denominator26 != 0, 1)
        VAR2 = (C - llv26) / denominator26 * 100

        # VAR3: SMA(SMA(VAR2,3,1),3,1)
        VAR3 = sma_tdx(sma_tdx(VAR2, 3, 1), 3, 1)

        # VAR4: EMA(VAR3,5)
        VAR4 = VAR3.ewm(span=5, adjust=False).mean()

        # VAR5: LLV(LOW,26)
        VAR5 = llv26

        # VAR6: HHV(HIGH,34)
        VAR6 = H.rolling(34).max()

        # VAR7: EMA((CLOSE-VAR5)/(VAR6-VAR5)*4,4)*25
        denominator_var7 = VAR6 - VAR5
        denominator_var7 = denominator_var7.where(denominator_var7 != 0, 1)
        VAR7 = ((C - VAR5) / denominator_var7 * 4).ewm(span=4, adjust=False).mean() * 25

        # VAR8: (2*C+H+L)/4
        VAR8 = (2 * C + H + L) / 4

        # VAR9: LLV(LOW,N1)
        VAR9 = L.rolling(N1).min()

        # VAR10: HHV(HIGH,N2)
        VAR10 = H.rolling(N2).max()

        # VAR2W: 100-100*(HHV(HIGH,14)-CLOSE)/(HHV(HIGH,14)-LLV(LOW,14))
        hhv14 = H.rolling(14).max()
        llv14 = L.rolling(14).min()
        denominator14 = hhv14 - llv14
        denominator14 = denominator14.where(denominator14 != 0, 1)
        VAR2W = 100 - 100 * (hhv14 - C) / denominator14

        # MW: EMA(VAR2W,3)
        MW = VAR2W.ewm(span=3, adjust=False).mean()

        # VAR3W: EMA(VAR2W,7)
        VAR3W = VAR2W.ewm(span=7, adjust=False).mean()

        # M1: EMA(VAR3W,5)
        M1 = VAR3W.ewm(span=5, adjust=False).mean()

        # MB1: CROSS(MW,M1) AND M1<20
        MB1 = cross_tdx(MW, M1) & (M1 < 20)

        # MG1: IF(CROSS(M1,MW) AND REF(MW,1)>80,80,0)
        cross_condition = cross_tdx(M1, MW) & (MW.shift(1) > 80)
        MG1 = cross_condition.astype(int) * 80

        # 核心指标计算
        # MJ: EMA((VAR8-VAR9)/(VAR10-VAR9)*100,9)
        denominator_mj = VAR10 - VAR9
        denominator_mj = denominator_mj.where(denominator_mj != 0, 1)
        MJ = ((VAR8 - VAR9) / denominator_mj * 100).ewm(span=9, adjust=False).mean()

        # TM: EMA(0.667*REF(MJ,1)+0.333*MJ,2)
        TM = (0.667 * MJ.shift(1) + 0.333 * MJ).ewm(span=2, adjust=False).mean()

        # 卖出条件：CROSS(80,MJ) - 80上穿MJ，即MJ从上方跌破80
        # 这里需要反向理解：80上穿MJ意味着MJ从上方跌破80
        sell_signal = (MJ.shift(1) > 80) & (MJ <= 80)

        if len(sell_signal) > 0 and sell_signal.iloc[-1]:
            return True
        else:
            return False

    except Exception as e:
        print(f"计算高抛低吸卖出信号出错 {stock}: {e}")
        return False


def check_enhanced_sell_signals(context, stock):
    """
    检查增强的卖出信号，返回卖出原因或None
    """
    try:
        # 1. 首先检查原有的技术卖出信号
        if calculate_gaopao_sell_signal(context, stock):
            return "技术信号"

        # 获取历史数据进行更多维度分析
        end_date = (context.now - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        start_date = (context.now - datetime.timedelta(days=60)).strftime('%Y-%m-%d')

        df = history(symbol=stock, frequency='1d', start_time=start_date, end_time=end_date,
                    fields='symbol,eob,open,close,high,low,volume', skip_suspended=True,
                    fill_missing='Last', adjust=ADJUST_PREV, df=True)

        if df is None or len(df) < 20:
            return None

        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']
        current_price = close.iloc[-1]

        # 2. 技术破位信号
        ma5 = close.rolling(5).mean()
        ma20 = close.rolling(20).mean()
        ma60 = close.rolling(60).mean() if len(close) >= 60 else ma20

        # 跌破重要均线
        if current_price < ma5.iloc[-1] * 0.95:  # 跌破5日线5%
            return "跌破5日线"
        if current_price < ma20.iloc[-1] * 0.92:  # 跌破20日线8%
            return "跌破20日线"

        # 3. 量价背离信号
        recent_prices = close.tail(10)
        recent_volumes = volume.tail(10)

        # 价格创新高但成交量萎缩
        if len(recent_prices) >= 10:
            price_trend = (recent_prices.iloc[-1] - recent_prices.iloc[-5]) / recent_prices.iloc[-5]
            volume_trend = (recent_volumes.tail(5).mean() - recent_volumes.head(5).mean()) / recent_volumes.head(5).mean()

            if price_trend > 0.05 and volume_trend < -0.3:  # 价涨量缩
                return "量价背离"

        # 4. 连续下跌信号
        recent_returns = close.pct_change().tail(5)
        consecutive_down = (recent_returns < -0.02).sum()  # 连续大跌天数
        if consecutive_down >= 3:
            return "连续下跌"

        # 5. 异常波动信号
        recent_volatility = close.pct_change().tail(10).std()
        if recent_volatility > 0.08:  # 波动率过高
            recent_decline = (current_price - close.iloc[-10]) / close.iloc[-10]
            if recent_decline < -0.15:  # 且近期大幅下跌
                return "异常波动"

        # 6. 根据市场环境调整卖出敏感度
        if context.market_state == "BEAR":
            # 熊市中更敏感的卖出
            if current_price < ma20.iloc[-1]:  # 跌破20日线即卖出
                return "熊市破位"
        elif context.market_state == "SHOCK":
            # 震荡市中适度敏感
            if current_price < ma5.iloc[-1] * 0.97:  # 跌破5日线3%
                return "震荡市破位"

        # 7. 高位风险信号
        recent_high = high.tail(20).max()
        if current_price < recent_high * 0.85:  # 从高点回撤15%
            return "高位回撤"

        return None

    except Exception as e:
        print(f"检查增强卖出信号出错 {stock}: {e}")
        return None


def check_delisting_risk(context, stock):
    """
    检查股票退市风险
    返回 (has_risk, risk_reasons)
    has_risk: True 表示有退市风险，应该避免买入
    risk_reasons: 风险原因列表
    """
    try:
        risk_reasons = []

        # 获取股票基本信息
        instruments = get_instrumentinfos(symbols=[stock])
        if not instruments:
            return True, ["无法获取股票信息"]

        stock_info = instruments[0]
        sec_name = stock_info.get('sec_name', '')

        # 1. 检查是否为ST股票（已在股票池中过滤，这里作为双重保险）
        if any(st_type in sec_name for st_type in ['ST', '*ST', 'S*ST', 'SST']):
            risk_reasons.append("ST股票风险")

        # 2. 获取近期价格数据进行风险分析 - 避免未来函数
        end_date = (context.now - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        start_date = (context.now - datetime.timedelta(days=60)).strftime('%Y-%m-%d')

        df = history(symbol=stock, frequency='1d', start_time=start_date, end_time=end_date,
                    fields='symbol,eob,close,high,low,volume', skip_suspended=True,
                    fill_missing='Last', adjust=ADJUST_PREV, df=True)

        if df is None or len(df) < 20:
            risk_reasons.append("历史数据不足")
            return True, risk_reasons

        # 3. 检查价格风险指标
        current_price = df['close'].iloc[-1]

        # 3.1 检查是否为仙股（价格过低）
        if current_price < 2.0:  # 股价低于2元
            risk_reasons.append(f"仙股风险(价格{current_price:.2f}元<2元)")

        # 3.2 检查连续跌停风险
        recent_prices = df['close'].tail(10)
        price_changes = recent_prices.pct_change().fillna(0)
        consecutive_limit_down = 0
        max_consecutive_limit_down = 0

        for change in price_changes:
            if change <= -0.095:  # 接近跌停（-9.5%以下）
                consecutive_limit_down += 1
                max_consecutive_limit_down = max(max_consecutive_limit_down, consecutive_limit_down)
            else:
                consecutive_limit_down = 0

        if max_consecutive_limit_down >= 3:  # 连续3个跌停
            risk_reasons.append(f"连续跌停风险(连续{max_consecutive_limit_down}个跌停)")

        # 3.3 检查成交量异常（可能停牌风险）
        recent_volumes = df['volume'].tail(10)
        zero_volume_days = (recent_volumes == 0).sum()
        if zero_volume_days >= 5:  # 近10天有5天以上零成交
            risk_reasons.append(f"成交量异常(近10天有{zero_volume_days}天零成交)")

        # 3.4 检查价格暴跌风险
        price_20_days_ago = df['close'].iloc[0] if len(df) >= 20 else current_price
        total_decline = (current_price - price_20_days_ago) / price_20_days_ago
        if total_decline < -0.5:  # 20天内跌幅超过50%
            risk_reasons.append(f"暴跌风险(20天跌幅{total_decline*100:.1f}%)")

        # 3.5 检查市值过小风险（通过价格和成交量估算）
        avg_volume = recent_volumes.tail(5).mean()
        estimated_market_cap = current_price * avg_volume * 100  # 粗略估算
        if estimated_market_cap < 1000000:  # 估算市值过小
            risk_reasons.append(f"市值过小风险(估算市值{estimated_market_cap/10000:.1f}万)")

        # 返回风险检测结果
        has_risk = len(risk_reasons) > 0
        return has_risk, risk_reasons

    except Exception as e:
        print(f"检查退市风险出错 {stock}: {e}")
        return True, [f"检查出错: {str(e)}"]  # 出错时保守处理，认为有风险


def get_stock_pool(context):
    """
    获取股票池，排除不符合条件的股票
    """
    try:
        # 获取所有A股股票
        stocks = get_instruments(exchanges=['SHSE', 'SZSE'], sec_types=[SEC_TYPE_STOCK])

        stock_list = []
        for stock in stocks:
            symbol = stock['symbol']
            sec_name = stock.get('sec_name', '')

            # 排除北证股票（代码以4或8开头）
            if symbol.startswith('4') or symbol.startswith('8'):
                continue

            # 严格排除ST股票（包括*ST、ST、S*ST、SST等各种ST类型）
            if any(st_type in sec_name for st_type in ['ST', '*ST', 'S*ST', 'SST']):
                continue

            stock_list.append(symbol)

        return stock_list

    except Exception as e:
        print(f"获取股票池出错: {e}")
        return []


def check_buy_signals(context):
    """
    检查买入信号（增强风险控制版本）
    """
    try:
        # 检查是否触发止损，如果是则暂停买入
        if context.stop_loss_triggered_today:
            print("今日已触发止损，暂停买入操作")
            return

        # 获取当前持仓信息
        account = context.account()
        positions = account.positions()
        current_position_count = len(positions)

        # 检查持仓数量限制（使用动态参数）
        if current_position_count >= context.dynamic_max_positions:
            print(f"持仓数量已达上限 {current_position_count}/{context.dynamic_max_positions}，暂停买入")
            return

        # 获取股票池
        stock_pool = get_stock_pool(context)
        if not stock_pool:
            print("股票池为空，跳过买入检查")
            return

        # 获取当前账户信息
        available_cash = account.cash['available']
        total_nav = account.cash['nav']  # 账户总资产

        # 计算单次买入金额 - 使用动态仓位比例
        buy_amount = total_nav * context.dynamic_position_ratio

        # 检查资金是否足够进行下次买入
        if buy_amount < 10000:  # 单次买入金额少于1万元时跳过
            print(f"买入金额不足: 账户总资产={total_nav:.2f}, 单次买入金额={buy_amount:.2f}")
            return

        # 检查可用资金是否足够
        if available_cash < buy_amount:
            print(f"可用资金不足: 可用资金={available_cash:.2f}, 需要买入金额={buy_amount:.2f}")
            return

        # 获取当前持仓股票列表，避免重复买入
        current_symbols = {p.symbol for p in positions}

        print(f"开始全市场扫描买入信号，股票池大小: {len(stock_pool)}...")
        print(f"当前持仓数量: {current_position_count}/{context.dynamic_max_positions}")
        print(f"市场环境: {context.market_state}, 动态仓位: {context.dynamic_position_ratio:.1%}")

        buy_signals = []
        # 遍历整个股票池来寻找买入信号
        for i, stock in enumerate(stock_pool):
            # 跳过已持仓的股票
            if stock in current_symbols:
                continue

            # 打印进度
            if (i + 1) % 500 == 0:
                print(f"  已扫描 {i + 1}/{len(stock_pool)}...")

            try:
                # 首先检查技术买入信号
                if calculate_yaodi_signal(context, stock):
                    # 然后检查退市风险
                    has_risk, risk_reasons = check_delisting_risk(context, stock)
                    if not has_risk:
                        # 进一步检查买入时机过滤条件
                        if check_buy_timing_filters(context, stock):
                            buy_signals.append(stock)

                            # 限制买入信号数量，避免过度分散
                            max_new_positions = context.dynamic_max_positions - current_position_count
                            if len(buy_signals) >= max_new_positions:
                                print(f"已找到足够的买入信号 {len(buy_signals)}/{max_new_positions}")
                                break
                        else:
                            # 获取股票名称用于提示
                            try:
                                inst = get_instrumentinfos(symbols=[stock])
                                stock_name = inst[0]['sec_name'] if inst else '未知名称'
                                print(f"跳过买入时机不佳的股票: {stock} ({stock_name})")
                            except:
                                print(f"跳过买入时机不佳的股票: {stock}")
                    else:
                        # 获取股票名称用于提示
                        try:
                            inst = get_instrumentinfos(symbols=[stock])
                            stock_name = inst[0]['sec_name'] if inst else '未知名称'
                            risk_detail = ", ".join(risk_reasons)
                            print(f"跳过有退市风险的股票: {stock} ({stock_name}) - 风险原因: {risk_detail}")
                        except:
                            risk_detail = ", ".join(risk_reasons)
                            print(f"跳过有退市风险的股票: {stock} - 风险原因: {risk_detail}")

            except Exception as e:
                print(f"检查股票 {stock} 买入信号出错: {e}")
                continue

        print(f"全市场扫描完成，共找到 {len(buy_signals)} 个买入信号。")

        # 一次性获取所有待买入股票的名称
        buy_name_map = {}
        if buy_signals:
            try:
                buy_instruments = get_instrumentinfos(symbols=buy_signals)
                buy_name_map = {inst['symbol']: inst['sec_name'] for inst in buy_instruments}
            except Exception as e:
                print(f"获取待买入股票名称时出错: {e}")

        # 执行买入 - 买入所有扫描到的信号
        successful_buys = 0
        for stock in buy_signals:
            try:
                # 再次检查持仓数量限制
                current_positions = len(context.account().positions())
                if current_positions >= context.dynamic_max_positions:
                    print(f"持仓数量已达上限，停止买入")
                    break

                # 获取当前价格
                current_data = current(symbols=[stock])
                if not current_data or current_data[0]['price'] <= 0:
                    print(f"无法获取 {stock} 当前价格或价格无效（可能停牌），跳过买入")
                    continue

                current_price = current_data[0]['price']
                shares = int(buy_amount / current_price / 100) * 100  # 按手买入
                actual_buy_value = shares * current_price  # 实际买入金额

                if shares >= 100:
                    order_target_value(symbol=stock,
                                       value=actual_buy_value,
                                       position_side=PositionSide_Long,
                                       order_type=OrderType_Market)

                    stock_name = buy_name_map.get(stock, '')
                    print(f"买入 {stock} ({stock_name}): {shares}股, 价格: {current_price:.2f}, 买入金额: {actual_buy_value:.2f}")
                    successful_buys += 1
                else:
                    print(f"{stock} 计算股数不足100股，跳过买入")

            except Exception as e:
                print(f"买入 {stock} 出错: {e}")

        print(f"成功买入 {successful_buys} 只股票")

    except Exception as e:
        print(f"检查买入信号出错: {e}")


def check_sell_signals(context):
    """
    检查卖出信号
    """
    try:
        # 获取当前持仓
        positions = context.account().positions()

        if not positions:
            print("当前无持仓，跳过卖出检查")
            return

        print(f"开始检查卖出信号，持仓股票数: {len(positions)}")

        # 获取持仓股票的基本信息
        symbols = [p.symbol for p in positions]
        try:
            instruments = get_instrumentinfos(symbols=symbols)
            name_map = {inst['symbol']: inst['sec_name'] for inst in instruments}
        except Exception as e:
            print(f"获取股票信息出错: {e}")
            name_map = {}

        for position in positions:
            stock = position.symbol
            stock_name = name_map.get(stock, '未知名称')

            try:
                # 检查是否为ST股票，如果是则无条件清仓
                if 'ST' in stock_name:
                    # 计算卖出盈亏
                    current_data = current(symbols=[stock])
                    if current_data and current_data[0]['price'] > 0:
                        current_price = current_data[0]['price']
                        cost_price = position.vwap  # 持仓成本价
                        pnl_amount = (current_price - cost_price) * position.volume
                        pnl_ratio = (current_price - cost_price) / cost_price * 100

                        order_target_percent(symbol=stock,
                                           percent=0,
                                           position_side=PositionSide_Long,
                                           order_type=OrderType_Market)
                        print(f"ST股票无条件清仓 {stock} ({stock_name}): 盈亏金额={pnl_amount:.2f}, 盈亏比例={pnl_ratio:.2f}%")
                    continue

                # 检查增强的卖出信号
                sell_reason = check_enhanced_sell_signals(context, stock)
                if sell_reason:
                    # 计算卖出盈亏
                    current_data = current(symbols=[stock])
                    if current_data and current_data[0]['price'] > 0:
                        current_price = current_data[0]['price']
                        cost_price = position.vwap  # 持仓成本价
                        pnl_amount = (current_price - cost_price) * position.volume
                        pnl_ratio = (current_price - cost_price) / cost_price * 100

                        order_target_percent(symbol=stock,
                                           percent=0,
                                           position_side=PositionSide_Long,
                                           order_type=OrderType_Market)
                        print(f"{sell_reason}卖出 {stock} ({stock_name}): 盈亏金额={pnl_amount:.2f}, 盈亏比例={pnl_ratio:.2f}%")

            except Exception as e:
                print(f"检查股票 {stock} 卖出信号出错: {e}")

    except Exception as e:
        print(f"检查卖出信号出错: {e}")





def log_positions(context):
    """
    记录当前持仓状态（增强风险监控版本）
    """
    try:
        account = context.account()
        positions = account.positions()

        # 计算当日盈亏
        current_nav = account.cash['nav']
        daily_pnl = 0.0
        if context.prev_nav is not None:
            daily_pnl = current_nav - context.prev_nav

        # 更新前一日净值
        context.prev_nav = current_nav

        # 计算总浮动盈亏比例
        total_fpnl = account.cash['fpnl']
        total_cost = current_nav - total_fpnl  # 总成本 = 当前净值 - 浮动盈亏
        fpnl_ratio = (total_fpnl / total_cost * 100) if total_cost > 0 else 0.0

        print(f"\n=== 账户状态 ===")
        print(f"总资产: {current_nav:.2f}")
        print(f"可用资金: {account.cash['available']:.2f}")
        print(f"浮动盈亏: {total_fpnl:.2f} ({fpnl_ratio:.2f}%)")
        print(f"当日盈亏: {daily_pnl:.2f}")
        print(f"持仓数量: {len(positions)}/{context.max_positions}")

        if not positions:
            print("当前无持仓")
            return

        # 风险监控分析
        print(f"\n=== 风险监控 ===")
        risk_analysis = analyze_portfolio_risk(context, positions, current_nav)

        print("持仓详情:")

        # 一次性获取所有持仓股票的附加信息
        symbols = [p.symbol for p in positions]
        instruments = get_instrumentinfos(symbols=symbols)
        daily_data = current(symbols=symbols)

        # 创建方便查询的字典
        name_map = {inst['symbol']: inst['sec_name'] for inst in instruments}
        daily_map = {bar['symbol']: bar for bar in daily_data}

        # 按持仓金额排序显示
        position_details = []
        for position in positions:
            symbol = position.symbol
            stock_name = name_map.get(symbol, '未知名称')
            floating_pnl = position.fpnl

            daily_info = daily_map.get(symbol)
            if daily_info and daily_info.get('price', 0) > 0:
                current_price = daily_info['price']
                position_value = current_price * position.volume
                cost_value = position.vwap * position.volume
                fpnl_ratio_individual = (floating_pnl / cost_value * 100) if cost_value > 0 else 0.0
                position_ratio = position_value / current_nav * 100

                # 计算持仓天数
                entry_date = context.position_entry_dates.get(symbol)
                holding_days = 0
                if entry_date:
                    holding_days = (context.now.date() - entry_date).days

                position_details.append({
                    'symbol': symbol,
                    'name': stock_name,
                    'value': position_value,
                    'ratio': position_ratio,
                    'pnl_ratio': fpnl_ratio_individual,
                    'holding_days': holding_days,
                    'current_price': current_price,
                    'volume': position.volume,
                    'floating_pnl': floating_pnl
                })

        # 按持仓金额排序
        position_details.sort(key=lambda x: x['value'], reverse=True)

        for detail in position_details:
            risk_flag = ""
            if detail['ratio'] > context.max_single_position * 100:
                risk_flag += "[集中度超标] "
            if detail['pnl_ratio'] <= context.stop_loss_ratio * 100:
                risk_flag += "[触及止损] "
            if detail['holding_days'] >= context.max_holding_days:
                risk_flag += "[持仓过久] "

            print(f"  {detail['symbol']} ({detail['name']}) {risk_flag}")
            print(f"    金额: {detail['value']:.2f} ({detail['ratio']:.1f}%), 盈亏: {detail['floating_pnl']:.2f} ({detail['pnl_ratio']:.1f}%)")
            print(f"    持仓: {detail['holding_days']}天, 价格: {detail['current_price']:.2f}, 数量: {detail['volume']}股")

    except Exception as e:
        print(f"记录持仓状态出错: {e}")


def analyze_portfolio_risk(context, positions, total_nav):
    """
    分析组合风险
    """
    try:
        if not positions:
            return {}

        # 获取当前价格数据
        symbols = [p.symbol for p in positions]
        daily_data = current(symbols=symbols)
        price_map = {bar['symbol']: bar['price'] for bar in daily_data if bar['price'] > 0}

        # 计算各项风险指标
        risk_metrics = {
            'concentration_risk': 0,  # 集中度风险
            'time_risk': 0,          # 时间风险
            'loss_risk': 0,          # 亏损风险
            'total_positions': len(positions)
        }

        max_position_ratio = 0
        overweight_positions = []
        long_holding_positions = []
        loss_positions = []

        for position in positions:
            symbol = position.symbol
            current_price = price_map.get(symbol, 0)

            if current_price > 0:
                # 计算持仓比例
                position_value = current_price * position.volume
                position_ratio = position_value / total_nav
                max_position_ratio = max(max_position_ratio, position_ratio)

                # 检查集中度风险
                if position_ratio > context.max_single_position:
                    overweight_positions.append((symbol, position_ratio))

                # 检查持仓时间风险
                entry_date = context.position_entry_dates.get(symbol)
                if entry_date:
                    holding_days = (context.now.date() - entry_date).days
                    if holding_days >= context.max_holding_days:
                        long_holding_positions.append((symbol, holding_days))

                # 检查亏损风险
                pnl_ratio = (current_price - position.vwap) / position.vwap
                if pnl_ratio <= context.stop_loss_ratio:
                    loss_positions.append((symbol, pnl_ratio))

        # 输出风险分析
        print(f"最大单股占比: {max_position_ratio:.1%} (限制: {context.max_single_position:.1%})")

        if overweight_positions:
            print(f"⚠️  集中度超标股票 ({len(overweight_positions)}只):")
            for symbol, ratio in overweight_positions:
                print(f"    {symbol}: {ratio:.1%}")

        if long_holding_positions:
            print(f"⚠️  持仓过久股票 ({len(long_holding_positions)}只):")
            for symbol, days in long_holding_positions:
                print(f"    {symbol}: {days}天")

        if loss_positions:
            print(f"🔴 触及止损股票 ({len(loss_positions)}只):")
            for symbol, pnl in loss_positions:
                print(f"    {symbol}: {pnl:.1%}")

        if not (overweight_positions or long_holding_positions or loss_positions):
            print("✅ 当前组合风险可控")

        return risk_metrics

    except Exception as e:
        print(f"分析组合风险出错: {e}")
        return {}


if __name__ == '__main__':
    """
    策略运行入口
    """
    run(strategy_id='yaodi_gaopao_strategy',
        filename='main.py',
        mode=MODE_BACKTEST,
        token='{{token}}',
        backtest_start_time='2025-01-01 08:00:00',
        backtest_end_time='2025-07-01 16:00:00',
        backtest_initial_cash=1000000,
        backtest_commission_ratio=0.0003,
        backtest_slippage_ratio=0.0001)
